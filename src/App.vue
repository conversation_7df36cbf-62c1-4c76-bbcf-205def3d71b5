<script setup>
import TodoInput from './components/todoInput.vue'
import TodoList from './components/todoList.vue'
import TodoDel from './components/todoDel.vue'
</script>

<template>
  <div class="container">
    <TodoInput />
    <TodoList />
    <TodoDel />
  </div>
</template>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 400px;
  height: 800px;
  background-color: #0f2b49;
  border-radius: 20px;
  box-shadow: 0 0 5px #c9c9c9;
}
</style>
