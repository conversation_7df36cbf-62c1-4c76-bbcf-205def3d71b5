<script setup>
import { ref } from 'vue'
import { useTodoStore } from '../stores/todoStore'
const todoStore = useTodoStore()
const todoText = ref('')

function enterAddTodo(e) {
  if (e.key === 'Enter') {
    todoStore.addTodo(todoText.value)
    todoText.value = ''
  }
}
</script>

<template>
  <div class="todoInput">
    <h1>my todo list</h1>
    <time datetime="2023-06-05">2023-06-05</time>
    <input
      type="text"
      placeholder="回车添加待办事项"
      v-model="todoText"
      @keyup="enterAddTodo"
    />
  </div>
</template>

<style scoped>
.todoInput {
  width: 100%;
  height: auto;
  text-align: center;
}
.todoInput h1 {
  font-size: 30px;
  color: #fff;
}
input::placeholder {
  color: #999; /* 灰色 */
}
.todoInput input {
  width: 80%;
  height: 40px;
  font-size: 16px;
  border: none;
  border-radius: 15px;
  outline: none;
  padding: 0 10px;
  margin-top: 20px;
  transition: all 0.3s ease;
}
.todoInput input:hover {
  transform: scale(1.05); /* 鼠标悬停时放大 */
  background-color: #e6c7a8; /* 鼠标悬停时变色 */
}
.todoInput input:focus {
  background-color: #e6c7a8; /* 聚焦时变色 */
  color: #3b3b3b;
}
time {
  display: block;
  font-size: 16px;
  text-align: right;
  margin-right: 30px;
  color: #fff;
}
</style>
