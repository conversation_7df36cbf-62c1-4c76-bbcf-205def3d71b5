<script setup>
import { useTodoStore } from '../stores/todoStore'
const todoStore = useTodoStore()
</script>

<template>
  <div class="todoList">
    <ul>
      <li v-for="todos in todoStore.todos" :key="todos.id">
        <label class="custom-checkbox">
          <input type="checkbox" v-model="todos.completed" />
          <span class="checkmark"></span>
        </label>
      </li>
      <li v-if="todoStore.totalCount === 0" class="empty-message">
        暂无待办事项
      </li>
    </ul>
  </div>
</template>

<style scoped>
.todoList {
  display: flex;
  width: 80%;
  height: 400px;
  text-align: center;
  margin: 20px auto;
}
.todoList ul {
  display: flex;

  flex-direction: column;
  width: 320px;
  height: 400px;
  list-style: none;

  padding: 0;
  margin: 0;
  gap: 10px;
}
.todoList li {
  display: flex;
  width: 100%; /* 确保li占据整行宽度 */
  text-align: left;
  color: #ffffff;
  border-bottom: #ffffff 1px solid;
}
.todo-text {
  margin-left: 10px;
  flex-grow: 1; /* 让文本占据中间所有可用空间 */
}
.custom-checkbox {
  display: inline-block;
  position: relative;
  cursor: pointer;
  user-select: none;
}

.del-btn {
  margin-left: auto;
}
</style>
