import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useTodoStore = defineStore('todos', () => {
  // 创建响应式数组存放todos数据
  const todos = ref([])

  // 准备好需要用到的计算属性
  const completedTodos = computed(() =>
    todos.value.filter((todo) => todo.completed)
  )

  const uncompletedTodos = computed(() =>
    todos.value.filter((todo) => !todo.completed)
  )

  const totalCount = computed(() => todos.value.length)

  // 添加操作方法
  // 1.回车键添加todos
  function addTodo(text) {
    if (text.trim()) {
      todos.value.push({
        id: Date.now(),
        text: text.trim(),
        completed: false
      })
    }
  }

  return { todos, completedTodos, uncompletedTodos, totalCount, addTodo }
})
